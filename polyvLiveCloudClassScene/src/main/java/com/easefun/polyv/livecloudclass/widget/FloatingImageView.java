package com.easefun.polyv.livecloudclass.widget;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.animation.DecelerateInterpolator;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.easefun.polyv.livecloudclass.util.DisplayUtil;

/***
 *可拖动的悬浮按钮
 */

public class FloatingImageView extends ConstraintLayout {
    private final static String TAG = "FloatingImageView";
    private int screenWidth;
    private int screenHeight;
    private int screenWidthHalf;
    private int statusHeight;
    private int lastX;
    private int lastY;
    private float downX;
    private float downY;
    private boolean isDrag;
    private boolean canDrag;
    private OnRestListener onRestListener;
    private AnimatorSet clickAnimatorSet;
    private AnimatorSet moveAnimatorSet;

    private static final int LEFT = -1;
    private static final int RIGHT = 1;
    private int orientation = RIGHT;

    public FloatingImageView(Context context) {
        super(context);
        init();
    }

    public FloatingImageView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public FloatingImageView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        screenWidth = DisplayUtil.getScreenWidth(getContext());
        screenWidthHalf = screenWidth / 2;
        screenHeight = DisplayUtil.getScreenHeight(getContext());
        statusHeight = DisplayUtil.getStatusBarHeight(getContext());

        canDrag = true;
    }


    /**
     * 设置为不可拖动
     *
     * @param canDrag
     */
    public void setCanDrag(boolean canDrag) {
        this.canDrag = canDrag;
        if(!canDrag) isDrag = false;
    }


    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if(canDrag) {
            int rawX = (int) event.getRawX();
            int rawY = (int) event.getRawY();
            switch (event.getAction() & MotionEvent.ACTION_MASK) {
                case MotionEvent.ACTION_DOWN:
                    getParent().requestDisallowInterceptTouchEvent(true);
                    lastX = rawX;
                    lastY = rawY;
                    downX = getX();
                    downY = getY();
                    Log.d("dddd", "down");
                    break;
                case MotionEvent.ACTION_MOVE:
                    isDrag = (Math.abs(getX() - downX) > 10) || (Math.abs(getY() - downY) > 10);
                    if(isDrag){
                        if(clickAnimatorSet != null) clickAnimatorSet.cancel();
                        if(moveAnimatorSet != null) moveAnimatorSet.cancel();
                    }
                    //计算手指移动了多少
                    int dx = rawX - lastX;
                    int dy = rawY - lastY;

                    float x = getX() + dx;
                    float y = getY() + dy;
                    //检测是否到达边缘 左上右下
                    x = x < 0 ? 0 : x > screenWidth - getWidth() ? screenWidth - getWidth() : x;
                    y = y < statusHeight ? statusHeight : y + getHeight() > screenHeight ? screenHeight - getHeight() : y;
                    setX(x);
                    setY(y);
                    lastX = rawX;
                    lastY = rawY;
                    Log.d("dddd", "move");
                    break;
                case MotionEvent.ACTION_UP:
                    if (isDrag) {
                        //恢复按压效果
                        setPressed(false);
                        if (rawX >= screenWidthHalf) {
                            orientation = RIGHT;
                            animate().setInterpolator(new DecelerateInterpolator())
                                    .setDuration(500)
                                    .xBy(screenWidth - getWidth() - getX())
                                    .start();
                        } else {
                            orientation = LEFT;
                            ObjectAnimator oa = ObjectAnimator.ofFloat(this, "x", getX(), 0);
                            oa.setInterpolator(new DecelerateInterpolator());
                            oa.setDuration(500);
                            oa.start();
                        }
                    }
                    Log.d("dddd", "up");
                    onRestListener.onUp(isDrag, isDrag ? 500 : 0);
                    isDrag = false;
                    break;
            }
        }
        //如果是拖拽则消耗事件，否则正常传递即可。
        return true;
    }

    public void show(){
        setVisibility(VISIBLE);
    }

    public void hide(){
        setVisibility(GONE);
    }

    public int getOrientation(){
        return orientation;
    }

    public void setClickAnimatorSet(AnimatorSet clickAnimatorSet) {
        this.clickAnimatorSet = clickAnimatorSet;
    }

    public AnimatorSet getClickAnimatorSet() {
        return clickAnimatorSet;
    }

    public AnimatorSet getMoveAnimatorSet() {
        return moveAnimatorSet;
    }

    public void setMoveAnimatorSet(AnimatorSet moveAnimatorSet) {
        this.moveAnimatorSet = moveAnimatorSet;
    }

    public void setOnRestListener(OnRestListener onRestListener) {
        this.onRestListener = onRestListener;
    }

    public interface OnRestListener{
        void onUp(boolean isDrag, int delay);
    }
}
