<?xml version="1.0" encoding="utf-8"?>
<com.biguo.utils.widget.StyleConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_marginStart="5dp"
    android:layout_marginEnd="5dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:all_round="10dp"
    app:bgGradientStartColor="@color/white"
    app:shadowWidth="8dp"
    app:shadowColor="@color/gray_gradient_2_bg">

    <com.biguo.utils.widget.StyleConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:layout_marginBottom="-4dp"
        app:bgGradientStartColor="@color/white"
        app:all_round="10dp"
        app:shadowWidth="8dp"
        app:shadowColor="@color/gray_gradient_2_bg"
        app:shadowOffsetY="4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/couponContentLayout"/>

    <com.biguo.utils.widget.StyleConstraintLayout
        android:id="@+id/couponContentLayout"
        android:minHeight="80dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:bgGradientStartColor="@color/white"
        app:all_round="10dp">

        <LinearLayout
            android:id="@+id/priceLayout"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:minWidth="80dp"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <com.dep.biguo.widget.DiversificationTextView
                android:id="@+id/priceView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="¥00.00"
                android:textColor="@color/orange"
                android:textSize="14dp"
                android:gravity="center"
                app:size="22dp"
                app:startChar="¥" />

            <TextView
                android:id="@+id/fullMinusPriceView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="满69可用"
                android:textColor="@color/orange"
                android:textSize="10dp" />

        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/dottedView"
            android:layout_width="10dp"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="@id/priceLayout"
            app:layout_constraintStart_toEndOf="@id/priceLayout"
            app:layout_constraintBottom_toBottomOf="@id/priceLayout">
            <com.biguo.utils.widget.StyleTextView
                android:layout_width="10dp"
                android:layout_height="5dp"
                app:left_bottom_round="10dp"
                app:right_bottom_round="10dp"
                app:bgGradientStartColor="@color/coupon_serration_color_1"
                app:bgGradientEndColor="@color/coupon_serration_color_2"
                app:bgcGradientAngle="90"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>

            <com.linsh.lshutils.view.DashedLine
                android:layout_width="1dp"
                android:layout_height="0dp"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                app:dashColor="@color/tblack4"
                app:dashGap="4dp"
                app:dashOrientation="vertical"
                app:dashWidth="4dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <com.biguo.utils.widget.StyleTextView
                android:layout_width="10dp"
                android:layout_height="5dp"
                app:left_top_round="10dp"
                app:right_top_round="10dp"
                app:bgGradientStartColor="@color/coupon_serration_color_2"
                app:bgGradientEndColor="@color/coupon_serration_color_1"
                app:bgcGradientAngle="90"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>


        <com.biguo.utils.widget.StyleConstraintLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:padding="10dp"
            app:layout_constraintStart_toEndOf="@id/dottedView"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">
            <TextView
                android:id="@+id/nameView"
                android:text="优惠券"
                android:textSize="14dp"
                android:textColor="@color/tblack"
                android:gravity="center_vertical"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                app:layout_constraintVertical_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/makeView"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toTopOf="@id/timeView"/>

            <com.dep.biguo.widget.DiversificationTextView
                android:id="@+id/timeView"
                android:text="到期"
                android:textSize="10dp"
                android:textColor="@color/tblack3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:layout_marginTop="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/nameView"
                app:layout_constraintEnd_toStartOf="@id/makeView"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <TextView
                android:id="@+id/makeView"
                android:text="去使用"
                android:textSize="13dp"
                android:textColor="@color/twhite"
                android:gravity="center"
                android:background="@drawable/bg_round_200_theme"
                android:layout_marginStart="34dp"
                android:layout_width="60dp"
                android:layout_height="28dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <ImageView
                android:id="@+id/unEnableView"
                android:src="@drawable/discount_wait_enable"
                android:layout_width="60dp"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>
        </com.biguo.utils.widget.StyleConstraintLayout>


    </com.biguo.utils.widget.StyleConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/couponContentLayout"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:id="@+id/openView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/arrow_right"
            android:rotation="90"
            android:layout_marginTop="13dp"
            android:layout_marginEnd="2dp"
            app:layout_constraintTop_toTopOf="@id/ruleView"
            app:layout_constraintEnd_toEndOf="@id/ruleView"/>

        <TextView
            android:id="@+id/ruleView"
            android:text="仅限购买VIP课堂、考前押密、视频课程时考前押密考前押密、视频课程时考前押密考前押密、视频课程时考前押密考前押密、视频课程时考前押密考前押密、视频课程时考前押密考前押密、视频课程时考前押密、考前押密、视频课程时视频课程时考前押密、考前押"
            android:textSize="10dp"
            android:textColor="@color/tblack3"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingTop="10dp"
            android:paddingBottom="8dp"
            android:paddingStart="0dp"
            android:paddingEnd="20dp"
            android:gravity="start"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.biguo.utils.widget.StyleConstraintLayout>