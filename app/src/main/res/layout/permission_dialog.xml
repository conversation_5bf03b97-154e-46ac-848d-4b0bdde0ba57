<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/bg_round_5_white">

    <TextView
        android:id="@+id/messageView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/app_space"
        android:layout_marginTop="@dimen/app_space"
        android:layout_marginRight="@dimen/app_space"
        android:background="@null"
        android:text="权限申请"
        android:paddingTop="@dimen/dp_5"
        android:paddingBottom="@dimen/dp_5"
        android:textColor="@color/tblack"
        android:textSize="@dimen/sp_16" />

    <LinearLayout
        android:id="@+id/permissionView"
        android:layout_below="@+id/messageView"
        android:layout_marginTop="4dp"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="15dp"
        android:paddingEnd="15dp"
        tools:itemCount="3"/>
    <TextView
        android:id="@+id/negativeView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/permissionView"
        android:layout_marginTop="16dp"
        android:layout_marginRight="@dimen/dp_5"
        android:layout_marginBottom="@dimen/dp_8"
        android:layout_toLeftOf="@+id/positiveView"
        android:background="@drawable/bg_rect_cwhite"
        android:clickable="true"
        android:paddingLeft="@dimen/dp_15"
        android:paddingTop="@dimen/dp_7"
        android:paddingRight="@dimen/dp_15"
        android:paddingBottom="@dimen/dp_7"
        android:text="拒绝"
        android:textColor="@color/orange"
        android:textSize="@dimen/sp_12" />

    <TextView
        android:id="@+id/positiveView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/permissionView"
        android:layout_alignParentRight="true"
        android:layout_marginTop="16dp"
        android:layout_marginRight="@dimen/app_space"
        android:layout_marginBottom="@dimen/dp_8"
        android:background="@drawable/bg_rect_cwhite"
        android:clickable="true"
        android:paddingLeft="@dimen/dp_15"
        android:paddingTop="@dimen/dp_7"
        android:paddingRight="@dimen/dp_15"
        android:paddingBottom="@dimen/dp_7"
        android:text="允许"
        android:textColor="@color/orange"
        android:textSize="@dimen/sp_12" />

</RelativeLayout>