<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="onClickListener"
            type="com.dep.biguo.mvp.ui.activity.ScholarshipRankActivity" />
    </data>

    <com.biguo.utils.widget.StyleConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:bgGradientStartColor="@color/socre_rank_start_bg"
        app:bgGradientEndColor="@color/socre_rank_end_bg"
        app:bgcGradientAngle="-45"
        app:bgGradientStartWeight="0.6"
        xmlns:app="http://schemas.android.com/apk/res-auto">
        <ImageView
            android:id="@+id/titleTrophy"
            android:src="@drawable/score_rank_trophy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="26dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <ImageView
            android:id="@+id/titleView"
            android:src="@drawable/score_rank_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="26dp"
            android:layout_marginTop="22dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:text="排名越前，奖金越高"
            android:textSize="12dp"
            android:textColor="@color/tblack"
            android:layout_marginTop="8dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="@id/titleView"
            app:layout_constraintTop_toBottomOf="@id/titleView"
            style="@style/lightText"/>

        <com.biguo.utils.widget.StyleLinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            app:bgGradientEndColor="@color/white"
            app:bgcGradientAngle="90"
            app:left_top_round="10dp"
            app:right_top_round="10dp"
            android:paddingBottom="52dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/titleTrophy"
            app:layout_constraintBottom_toBottomOf="parent">
            <LinearLayout
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:text="当前科目"
                    android:textSize="14dp"
                    android:textColor="@color/tblack"
                    android:lines="1"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:paddingStart="15dp"
                    android:paddingEnd="0dp"
                    android:layout_width="83dp"
                    android:layout_height="36dp"
                    style="@style/lightText"/>

                <TextView
                    android:id="@+id/courseView"
                    android:text="xxxx"
                    android:textSize="14dp"
                    android:textColor="@color/tblack"
                    android:lines="1"
                    android:ellipsize="end"
                    android:gravity="center_vertical|end"
                    android:paddingStart="15dp"
                    android:paddingEnd="27dp"
                    android:layout_width="match_parent"
                    android:layout_height="36dp"
                    style="@style/lightText"/>
            </LinearLayout>

            <RelativeLayout
                android:id="@+id/rankTitleLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">
                <TextView
                    android:text="排名"
                    android:textColor="@color/tblack2"
                    android:textSize="14dp"
                    android:gravity="center"
                    android:layout_width="58dp"
                    android:layout_height="wrap_content"
                    style="@style/lightText"/>

                <TextView
                    android:text="用户ID"
                    android:textColor="@color/tblack2"
                    android:textSize="14dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="83dp"
                    android:layout_marginEnd="100dp"
                    style="@style/lightText"/>

                <TextView
                    android:text="科目成绩"
                    android:textColor="@color/tblack2"
                    android:textSize="14dp"
                    android:gravity="center"
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="15dp"
                    android:layout_alignParentEnd="true"
                    style="@style/lightText"/>
            </RelativeLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/receiveView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="8dp"
                tools:listitem="@layout/scholarship_rank_item"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>
        </com.biguo.utils.widget.StyleLinearLayout>

        <com.biguo.utils.widget.StyleConstraintLayout
            android:id="@+id/myselfLayout"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:layout_marginStart="-2dp"
            android:layout_marginEnd="-2dp"
            app:bgGradientStartColor="@color/white"
            app:shadowColor="@color/gray_gradient_1_bg"
            app:shadowWidth="4dp"
            app:shadowOffsetY="-4dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">
            <TextView
                android:id="@+id/NoView"
                android:text="0"
                android:textColor="@color/tblack2"
                android:textSize="16dp"
                android:layout_centerVertical="true"
                android:gravity="center"
                android:layout_width="58dp"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <com.dep.biguo.widget.RoundedImageView
                android:id="@+id/avatarView"
                android:src="@drawable/default_avatar"
                android:layout_centerVertical="true"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_marginStart="83dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <TextView
                android:id="@+id/nameView"
                android:text="我"
                android:textSize="16dp"
                android:textColor="@color/tblack"
                android:layout_centerVertical="true"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                style="@style/lightText"
                app:layout_constraintStart_toEndOf="@id/avatarView"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toStartOf="@id/scoreView"
                app:layout_constraintBottom_toBottomOf="parent"/>


            <TextView
                android:id="@+id/scoreView"
                android:text="0"
                android:textColor="#FA6C64"
                android:textSize="16dp"
                android:gravity="center"
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="15dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>
        </com.biguo.utils.widget.StyleConstraintLayout>
    </com.biguo.utils.widget.StyleConstraintLayout>

</layout>