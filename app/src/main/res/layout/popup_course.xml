<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/flContent"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#90000000"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_430"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_round_bot_10_bgc"
            android:orientation="vertical">
            <TextView
                android:paddingBottom="@dimen/dp_10"
                android:textStyle="bold"
                android:textSize="@dimen/sp_13"
                android:id="@+id/tvSchool"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="学校"
                android:textColor="@color/tblack" />
            <TextView
                android:textStyle="bold"
                android:textSize="@dimen/sp_13"
                android:id="@+id/tvProfession"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="专业"
                android:textColor="@color/tblack" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:layout_marginLeft="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_10"
                android:text="@string/home_course_ybk"
                android:textColor="@color/tblack2"
                android:textSize="@dimen/sp_13" />

            <TextView
                android:id="@+id/tvEmpty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:layout_gravity="center_horizontal"
                android:layout_margin="@dimen/dp_15"
                android:text="添加报考的课程可快速切换"
                android:textColor="@color/tblack3"
                android:textSize="@dimen/sp_12" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvCourse"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/tvManager"
                android:layout_width="@dimen/dp_120"
                android:layout_height="@dimen/dp_30"
                android:visibility="gone"
                android:layout_gravity="center_horizontal"
                android:layout_margin="@dimen/dp_10"
                android:background="@drawable/bg_round_200_theme"
                android:gravity="center"
                android:text="@string/home_course_manager"
                android:textColor="@color/twhite"
                android:textSize="@dimen/sp_13"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>

</FrameLayout>