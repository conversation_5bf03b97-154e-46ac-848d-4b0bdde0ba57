<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#33000000">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginLeft="@dimen/dp_30"
        android:layout_marginRight="@dimen/dp_30"
        android:background="@drawable/bg_round_5_white"
        android:paddingLeft="@dimen/app_space"
        android:paddingTop="@dimen/app_space"
        android:paddingRight="@dimen/app_space">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:tag="beta_title"
            android:text="标题"
            android:textColor="@color/tblack"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:id="@+id/tvInfo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tvTitle"
            android:layout_marginTop="@dimen/app_space"
            android:textSize="@dimen/sp_12"
            android:text="更新说明"
            android:textColor="@color/tblack2" />

        <TextView
            android:id="@+id/tvFeature"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tvInfo"
            android:layout_marginTop="@dimen/dp_5"
            android:tag="beta_upgrade_feature"
            android:text="新特性"
            android:textSize="@dimen/sp_12"
            android:textColor="@color/tblack" />

        <TextView
            android:id="@+id/tvNegative"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tvFeature"
            android:layout_marginTop="@dimen/dp_35"
            android:layout_marginRight="@dimen/dp_5"
            android:layout_marginBottom="@dimen/dp_8"
            android:layout_toLeftOf="@+id/tvConfirm"
            android:background="@drawable/bg_rect_cwhite"
            android:clickable="true"
            android:paddingLeft="@dimen/dp_15"
            android:paddingTop="@dimen/dp_7"
            android:paddingRight="@dimen/dp_15"
            android:paddingBottom="@dimen/dp_7"
            android:tag="beta_cancel_button"
            android:text="下次再说"
            android:textColor="@color/orange"
            android:textSize="@dimen/sp_12" />

        <TextView
            android:id="@+id/tvConfirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tvFeature"
            android:layout_alignParentRight="true"
            android:layout_marginTop="@dimen/dp_35"
            android:layout_marginBottom="@dimen/dp_8"
            android:background="@drawable/bg_rect_cwhite"
            android:clickable="true"
            android:paddingLeft="@dimen/dp_15"
            android:paddingTop="@dimen/dp_7"
            android:paddingRight="@dimen/dp_15"
            android:paddingBottom="@dimen/dp_7"
            android:tag="beta_confirm_button"
            android:text="@string/confirm"
            android:textColor="@color/orange"
            android:textSize="@dimen/sp_12" />

    </RelativeLayout>

</FrameLayout>