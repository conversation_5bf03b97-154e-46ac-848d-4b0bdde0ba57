<?xml version="1.0" encoding="utf-8"?>
<layout>
    <data>
        <variable
            name="onClickListener"
            type="com.dep.biguo.widget.HomeNewHeadView" />
    </data>
    <ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/bannerLayout"
        android:background="@color/white"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:nestedScrollingEnabled="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.youth.banner.Banner
                android:id="@+id/headBannerView"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="15dp"
                android:layout_marginEnd="15dp"
                app:layout_constraintDimensionRatio="h,345:120"
                app:indicator_drawable_selected="@drawable/home_banner_indicator_s"
                app:indicator_drawable_unselected="@drawable/home_banner_indicator_n"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/courseAllLayout"
                android:gravity="center"
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="15dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/headBannerView"
                app:layout_constraintEnd_toEndOf="parent">
                <com.google.android.material.tabs.TabLayout
                    android:id="@+id/tabLayout"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="45dp"
                    app:tabTextAppearance="@style/TabTextSize"
                    app:tabIndicatorFullWidth="false"
                    app:tabMode="scrollable"
                    app:tabTextColor="@color/tblack"
                    app:tabSelectedTextColor="@color/tblack"
                    app:tabIndicatorHeight="0dp"/>

                <ImageView
                    android:id="@+id/moreView"
                    android:src="@drawable/true_paper_all_new_more"
                    android:paddingStart="10dp"
                    android:paddingEnd="0dp"
                    android:onClick="@{onClickListener.onClick}"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    app:tint="@color/tblack" />
            </LinearLayout>


            <ImageView
                android:id="@+id/newUserActivityView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="56dp"
                android:layout_marginStart="15dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="15dp"
                android:onClick="@{onClickListener.onClick}"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/courseAllLayout"
                app:layout_constraintEnd_toEndOf="parent"/>

            <com.dep.biguo.widget.pagemenu.PageMenuLayout
                android:id="@+id/pageMenuView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="140dp"
                android:layout_marginTop="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/newUserActivityView"
                app:layout_constraintEnd_toEndOf="parent"/>

            <RadioGroup
                android:id="@+id/indicatorGroup"
                android:orientation="horizontal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintBottom_toBottomOf="@id/pageMenuView"
                app:layout_constraintEnd_toEndOf="parent"/>

            <View
                android:id="@+id/line0View"
                android:background="@color/line"
                android:layout_marginTop="10dp"
                android:layout_width="match_parent"
                android:layout_height="5dp"
                app:layout_constraintStart_toStartOf="@id/pageMenuView"
                app:layout_constraintTop_toBottomOf="@id/pageMenuView"
                app:layout_constraintEnd_toEndOf="@id/pageMenuView"/>

            <!--通过ItemDecoration调整item的间距-->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/iconRecyclerView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:nestedScrollingEnabled="false"
                app:layout_constraintStart_toStartOf="@id/line0View"
                app:layout_constraintTop_toBottomOf="@id/line0View"
                app:layout_constraintEnd_toEndOf="@id/line0View"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="5"
                tools:itemCount="5"
                tools:listitem="@layout/icon_view"/>

            <com.youth.banner.Banner
                android:id="@+id/middleBannerView"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:minHeight="60dp"
                android:layout_marginStart="15dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="15dp"
                app:layout_constraintDimensionRatio="h,1117:200"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iconRecyclerView"
                app:indicator_drawable_selected="@drawable/home_banner_indicator_s"
                app:indicator_drawable_unselected="@drawable/home_banner_indicator_n" />

            <View
                android:id="@+id/line1View"
                android:background="@color/line"
                android:layout_width="match_parent"
                android:layout_height="5dp"
                android:layout_marginTop="10dp"
                app:layout_constraintTop_toBottomOf="@id/middleBannerView"/>

            <TextView
                android:id="@+id/groupTitleView"
                android:text="考试神器-快来拼团吧"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/tblack"
                android:textSize="18dp"
                android:layout_marginTop="10dp"
                style="@style/boldText"
                app:layout_constraintStart_toStartOf="@id/courseAllLayout"
                app:layout_constraintTop_toBottomOf="@id/line1View"/>

            <ImageView
                android:id="@+id/secretGroupView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:minHeight="80dp"
                android:layout_marginTop="10dp"
                android:onClick="@{onClickListener.onClick}"
                app:layout_constraintEnd_toStartOf="@id/vipGroupView"
                app:layout_constraintStart_toStartOf="@id/groupTitleView"
                app:layout_constraintTop_toBottomOf="@id/groupTitleView" />

            <ImageView
                android:id="@+id/vipGroupView"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="10dp"
                android:onClick="@{onClickListener.onClick}"
                app:layout_constraintStart_toEndOf="@id/secretGroupView"
                app:layout_constraintTop_toTopOf="@id/secretGroupView"
                app:layout_constraintEnd_toEndOf="@id/courseAllLayout"
                app:layout_constraintBottom_toBottomOf="@id/secretGroupView"/>

            <View
                android:id="@+id/line2View"
                android:background="@color/line"
                android:layout_width="match_parent"
                android:layout_height="5dp"
                android:layout_marginTop="10dp"
                app:layout_constraintTop_toBottomOf="@id/secretGroupView"/>

            <TextView
                android:id="@+id/recommendTitleView"
                android:layout_width="wrap_content"
                android:layout_height="24dp"
                android:gravity="center"
                android:text="考试必备"
                android:textSize="18dp"
                android:textColor="@color/tblack"
                android:layout_marginTop="10dp"
                android:onClick="@{onClickListener.onClick}"
                style="@style/boldText"
                app:layout_constraintStart_toStartOf="@id/courseAllLayout"
                app:layout_constraintTop_toBottomOf="@id/line2View"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recommendRecyclerView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:orientation="horizontal"
                tools:listitem="@layout/home_recommend_item"
                tools:itemCount="4"
                app:layoutManager=".widget.MaxCountItemManager"
                app:layout_constraintStart_toStartOf="@id/courseAllLayout"
                app:layout_constraintTop_toBottomOf="@id/recommendTitleView"
                app:layout_constraintEnd_toEndOf="@id/courseAllLayout"/>

            <View
                android:id="@+id/line5View"
                android:background="@color/line"
                android:layout_width="match_parent"
                android:layout_height="5dp"
                android:layout_marginTop="10dp"
                app:layout_constraintTop_toBottomOf="@id/recommendRecyclerView"/>

            <TextView
                android:id="@+id/examNewsView"
                android:layout_width="wrap_content"
                android:layout_height="24dp"
                android:gravity="center"
                android:text="考试资讯"
                android:textSize="18dp"
                android:textColor="@color/tblack"
                android:layout_marginTop="10dp"
                android:onClick="@{onClickListener.onClick}"
                style="@style/boldText"
                app:layout_constraintStart_toStartOf="@id/courseAllLayout"
                app:layout_constraintTop_toBottomOf="@id/line5View"/>

            <TextView
                android:id="@+id/schoolNewsView"
                android:layout_width="wrap_content"
                android:layout_height="24dp"
                android:gravity="center"
                android:text="学校新闻"
                android:textSize="14dp"
                android:textColor="@color/tblack3"
                android:layout_marginStart="24dp"
                android:onClick="@{onClickListener.onClick}"
                style="@style/boldText"
                app:layout_constraintStart_toEndOf="@id/examNewsView"
                app:layout_constraintTop_toTopOf="@id/examNewsView"
                app:layout_constraintBottom_toBottomOf="@id/examNewsView"/>

            <TextView
                android:id="@+id/moreNewsView"
                android:text="更多"
                android:drawablePadding="4dp"
                android:drawableEnd="@drawable/arrow_right"
                android:gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:textSize="12dp"
                android:textColor="@color/tblack2"
                android:onClick="@{onClickListener.onClick}"
                app:layout_constraintTop_toTopOf="@id/examNewsView"
                app:layout_constraintBottom_toBottomOf="@id/examNewsView"
                app:layout_constraintEnd_toEndOf="@id/courseAllLayout" />

            <ImageView
                android:id="@+id/newsIndicatorView"
                android:src="@drawable/home_news_indicator"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                app:layout_constraintTop_toBottomOf="@id/examNewsView"
                app:layout_constraintStart_toStartOf="@id/examNewsView"
                app:layout_constraintEnd_toEndOf="@id/examNewsView"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/newsRecyclerView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                tools:itemCount="1"
                tools:listitem="@layout/home_article_item"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintStart_toStartOf="@id/courseAllLayout"
                app:layout_constraintEnd_toEndOf="@id/courseAllLayout"
                app:layout_constraintTop_toBottomOf="@id/newsIndicatorView"/>


            <View
                android:id="@+id/line6View"
                android:background="@color/line"
                android:layout_width="match_parent"
                android:layout_height="5dp"
                android:layout_marginTop="10dp"
                app:layout_constraintTop_toBottomOf="@id/newsRecyclerView"/>

            <TextView
                android:id="@+id/circleTitleView"
                android:layout_width="wrap_content"
                android:layout_height="24dp"
                android:gravity="center"
                android:text="圈子动态"
                android:textSize="18dp"
                android:textColor="@color/tblack"
                android:layout_marginTop="10dp"
                android:onClick="@{onClickListener.onClick}"
                style="@style/boldText"
                app:layout_constraintStart_toStartOf="@id/courseAllLayout"
                app:layout_constraintTop_toBottomOf="@id/line6View"/>

            <TextView
                android:id="@+id/moreCircleView"
                android:text="更多"
                android:drawablePadding="4dp"
                android:drawableEnd="@drawable/arrow_right"
                android:gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:textSize="12dp"
                android:textColor="@color/tblack2"
                android:onClick="@{onClickListener.onClick}"
                app:layout_constraintTop_toTopOf="@id/circleTitleView"
                app:layout_constraintBottom_toBottomOf="@id/circleTitleView"
                app:layout_constraintEnd_toEndOf="@id/courseAllLayout" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>
</layout>
