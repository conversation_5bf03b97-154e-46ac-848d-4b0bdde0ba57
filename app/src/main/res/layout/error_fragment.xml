<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="onClickListener"
            type="com.dep.biguo.mvp.ui.fragment.ErrorFragment" />
    </data>
    <com.dep.biguo.widget.SmartRefreshLayout
        android:id="@+id/swipeLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.core.widget.NestedScrollView
            android:fillViewport="true"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:gravity="center_vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="15dp"
                    android:layout_marginBottom="10dp">
                    <TextView
                        android:text="错题统计"
                        android:textSize="18dp"
                        android:textColor="@color/tblack"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        style="@style/boldText"/>

                    <TextView
                        android:id="@+id/clearView"
                        android:text="清空"
                        android:textSize="14dp"
                        android:textColor="@color/tblack"
                        android:drawableStart="@drawable/error_coll_clear"
                        android:drawablePadding="4dp"
                        android:onClick="@{onClickListener}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                </LinearLayout>

                <com.biguo.utils.widget.StyleConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:paddingStart="15dp"
                    android:paddingTop="10dp"
                    android:paddingEnd="15dp"
                    android:paddingBottom="10dp"
                    app:all_round="6dp"
                    app:bgGradientStartColor="@color/white">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="答对后自动移除错题"
                        android:textColor="@color/tblack"
                        android:textSize="14dp"
                        app:layout_constraintBottom_toBottomOf="@id/removeErrorView"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="@id/removeErrorView" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/removeErrorView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end|center_vertical"
                        android:theme="@style/SwitchStyle"
                        android:thumb="@drawable/switch_thumb"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:track="@drawable/switch_track" />

                    <!--<RadioGroup
                        android:id="@+id/removeStandardLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="10dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/removeErrorView">
                        <RadioButton
                            android:id="@+id/correctOneRemoveView"
                            android:text="答对1次就移除"
                            android:textColor="@color/tblack2"
                            android:textSize="14dp"
                            android:checked="true"
                            android:layout_marginStart="-6dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>

                        <RadioButton
                            android:id="@+id/correctThreeRemoveView"
                            android:text="答对3次才移除"
                            android:textColor="@color/tblack2"
                            android:textSize="14dp"
                            android:layout_marginStart="36dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                    </RadioGroup>-->
                </com.biguo.utils.widget.StyleConstraintLayout>

                <com.biguo.utils.widget.StyleLinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="15dp"
                    android:padding="10dp"
                    app:all_round="6dp"
                    app:bgGradientStartColor="@color/white">
                    <com.dep.biguo.widget.DiversificationTextView
                        android:id="@+id/allErrorCountView"
                        android:text="错题总数\n0"
                        android:textSize="14dp"
                        android:textColor="@color/tblack2"
                        android:gravity="center"
                        android:lineSpacingExtra="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        app:startChar="\n"
                        app:size="22dp"
                        app:changeColor="@color/tblack"/>

                    <View
                        android:background="@color/line"
                        android:layout_marginTop="5dp"
                        android:layout_marginBottom="5dp"
                        android:layout_width="1dp"
                        android:layout_height="match_parent"/>

                    <com.dep.biguo.widget.DiversificationTextView
                        android:id="@+id/dayErrorCountView"
                        android:text="今日错题\n0"
                        android:textSize="14dp"
                        android:textColor="@color/tblack2"
                        android:gravity="center"
                        android:lineSpacingExtra="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        app:startChar="\n"
                        app:size="22dp"
                        app:changeColor="@color/tblack"/>

                    <View
                        android:background="@color/line"
                        android:layout_marginTop="5dp"
                        android:layout_marginBottom="5dp"
                        android:layout_width="1dp"
                        android:layout_height="match_parent"/>

                    <LinearLayout
                        android:orientation="vertical"
                        android:gravity="center"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="0.6">
                        <com.dep.biguo.widget.DiversificationTextView
                            android:text="错题/已做题"
                            android:textSize="14dp"
                            android:textColor="@color/tblack2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"/>
                        <com.dep.biguo.widget.DiversificationTextView
                            android:id="@+id/errorRateView"
                            android:text="0%"
                            android:textSize="22dp"
                            android:textColor="@color/theme"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:startChar="%"
                            app:isStartFlag="true"
                            app:size="14dp"
                            app:changeColor="@color/theme"/>
                    </LinearLayout>
                </com.biguo.utils.widget.StyleLinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:orientation="vertical"
                    android:paddingTop="20dp"
                    android:paddingBottom="10dp"
                    android:background="@drawable/error_coll_bg">

                    <TextView
                        android:text="错题分布"
                        android:textSize="18dp"
                        android:textColor="@color/tblack"
                        android:layout_marginStart="15dp"
                        android:layout_marginEnd="15dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        tools:listitem="@layout/myerror_rv_item"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>

                </LinearLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </com.dep.biguo.widget.SmartRefreshLayout>
</layout>