<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/iconView"
        android:layout_width="40dp"
        android:layout_height="40dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <ImageView
        android:id="@+id/tipImageView"
        android:src="@drawable/home_new_user_day_card"
        android:scaleType="fitCenter"
        android:layout_width="30dp"
        android:layout_height="15dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@id/iconView"
        app:layout_constraintEnd_toEndOf="@id/iconView"/>

    <com.biguo.utils.widget.StyleTextView
        android:id="@+id/tipTextView"
        android:text="0"
        android:textSize="9dp"
        android:textColor="@color/twhite"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:minWidth="12dp"
        android:minHeight="12dp"
        android:paddingStart="2dp"
        android:paddingEnd="2dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@id/iconView"
        app:layout_constraintEnd_toEndOf="@id/iconView"
        app:all_round="10dp"
        app:bgGradientStartColor="@color/theme"/>

    <TextView
        android:id="@+id/textView"
        android:textSize="12dp"
        android:textColor="@color/tblack"
        android:layout_marginTop="10dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iconView"
        app:layout_constraintEnd_toEndOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>