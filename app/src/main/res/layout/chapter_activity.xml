<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="onClickListener"
            type="com.dep.biguo.mvp.ui.activity.ChapterActivity" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:orientation="vertical"
        android:background="@color/white"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/backImageView"
            android:background="@drawable/topic_list_bg"
            android:layout_width="match_parent"
            android:layout_height="280dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <com.dep.biguo.widget.SmartRefreshLayout
            android:id="@+id/swipeLayout"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <FrameLayout
                        android:id="@+id/headLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/noGradeLayout"
                            android:visibility="gone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <TextView
                                android:id="@+id/noGradeView"
                                android:text="暂无分数"
                                android:textSize="16dp"
                                android:textColor="@color/tblack"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="20dp"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"/>

                            <TextView
                                android:id="@+id/noGradeDescView"
                                android:text="做题完成后才能为您评估分数"
                                android:textSize="12dp"
                                android:textColor="@color/tblack3"
                                style="@style/lightText"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                app:layout_constraintStart_toStartOf="@id/noGradeView"
                                app:layout_constraintTop_toBottomOf="@id/noGradeView"/>


                            <com.biguo.utils.widget.StyleTextView
                                android:id="@+id/doQuestionView"
                                android:text="前往做题"
                                android:textSize="12dp"
                                android:textColor="@color/twhite"
                                android:gravity="center_vertical"
                                style="@style/lightText"
                                android:drawablePadding="4dp"
                                android:drawableEnd="@drawable/arrow_right_write"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingStart="10dp"
                                android:paddingTop="1dp"
                                android:paddingEnd="10dp"
                                android:paddingBottom="1dp"
                                android:layout_marginTop="10dp"
                                android:onClick="@{onClickListener}"
                                android:visibility="gone"
                                app:all_round="10dp"
                                app:bgGradientStartColor="#FE5656"
                                app:layout_constraintStart_toStartOf="@id/noGradeView"
                                app:layout_constraintTop_toBottomOf="@id/noGradeDescView"/>

                            <LinearLayout
                                android:id="@+id/otherGradeLayout"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/other_people_grade"
                                android:gravity="center"
                                android:orientation="vertical"
                                android:layout_marginTop="4dp"
                                android:layout_marginEnd="20dp"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="parent">

                                <com.xubo.scrolltextview.ScrollTextView
                                    android:id="@+id/scrollUserView"
                                    style="@style/lightText"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginBottom="6dp"
                                    app:textColor="#2685FF"
                                    app:textSize="10dp" />
                            </LinearLayout>

                            <ImageView
                                android:id="@+id/aiView"
                                android:src="@drawable/ai"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                app:layout_constraintStart_toStartOf="@+id/otherGradeLayout"
                                app:layout_constraintTop_toBottomOf="@id/otherGradeLayout"
                                app:layout_constraintEnd_toEndOf="@id/otherGradeLayout"/>
                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/gradeLayout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <LinearLayout
                                android:id="@+id/estimateGradeLayoutView"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:layout_marginStart="20dp"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent">
                                <TextView
                                    android:id="@+id/estimateGradeTitleView"
                                    android:text="笔果AI预估分数"
                                    android:textColor="@color/tblack"
                                    android:textSize="12dp"
                                    style="@style/lightText"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"/>

                                <TextView
                                    android:id="@+id/estimateGradeView"
                                    android:text="100.00"
                                    android:textColor="@color/theme"
                                    android:textSize="28dp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"/>

                                <com.dep.biguo.widget.DiversificationTextView
                                    android:id="@+id/doRotateView"
                                    android:text="完成率 0%"
                                    android:textColor="@color/tblack2"
                                    android:textSize="12dp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    app:start="3"
                                    app:changeColor="@color/theme"/>
                            </LinearLayout>

                            <TextView
                                android:id="@+id/maroThanUserView"
                                android:text="超过0%同学"
                                android:textSize="10dp"
                                android:textColor="@color/twhite"
                                android:background="@drawable/topic_list_do_rate"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10dp"
                                android:paddingStart="5dp"
                                app:layout_constraintStart_toEndOf="@+id/estimateGradeLayoutView"
                                app:layout_constraintTop_toTopOf="@id/estimateGradeLayoutView"
                                app:layout_constraintBottom_toBottomOf="@id/estimateGradeLayoutView"/>

                            <com.biguo.utils.widget.StyleLinearLayout
                                android:id="@+id/aiLayout"
                                android:layout_width="110dp"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:layout_marginStart="20dp"
                                android:layout_marginEnd="32dp"
                                android:background="@drawable/topic_list_ai_bg"
                                android:onClick="@{onClickListener}"
                                android:paddingStart="10dp"
                                android:paddingTop="6dp"
                                android:paddingEnd="10dp"
                                android:paddingBottom="6dp"
                                app:layout_constrainedWidth="true"
                                app:layout_constrainedHeight="true"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintBottom_toBottomOf="@id/estimateGradeLayoutView">

                                <TextView
                                    android:id="@+id/aiMessageView"
                                    android:text="已为您生成一份专属过考方案"
                                    android:textSize="10dp"
                                    android:textColor="#2685FF"
                                    style="@style/lightText"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"/>
                                <TextView
                                    android:id="@+id/aiOperateView"
                                    android:text="联系老师领取>>"
                                    android:textSize="10dp"
                                    android:textColor="#2685FF"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"/>
                            </com.biguo.utils.widget.StyleLinearLayout>

                            <ImageView
                                android:src="@drawable/ai"
                                android:layout_width="35dp"
                                android:layout_height="31dp"
                                android:layout_marginEnd="-12dp"
                                android:layout_marginBottom="-5dp"
                                app:layout_constraintEnd_toEndOf="@id/aiLayout"
                                app:layout_constraintBottom_toBottomOf="@id/aiLayout"/>
                        </androidx.constraintlayout.widget.ConstraintLayout>
                    </FrameLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/statisticsLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <LinearLayout
                            android:id="@+id/dayDoCountLayout"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:layout_marginTop="4dp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/line1View">

                            <TextView
                                android:id="@+id/dayCountView"
                                android:text="0"
                                android:textColor="@color/tblack"
                                android:textSize="16dp"
                                style="@style/lightText"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>

                            <TextView
                                android:text="今日做题"
                                android:textColor="@color/tblack3"
                                android:textSize="10dp"
                                style="@style/lightText"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>

                        <View
                            android:id="@+id/line1View"
                            android:background="@color/line_color"
                            android:layout_width="1dp"
                            android:layout_height="15dp"
                            app:layout_constraintStart_toEndOf="@id/dayDoCountLayout"
                            app:layout_constraintTop_toTopOf="@id/dayDoCountLayout"
                            app:layout_constraintBottom_toBottomOf="@id/dayDoCountLayout"
                            app:layout_constraintEnd_toStartOf="@id/allDoCountLayout"/>

                        <LinearLayout
                            android:id="@+id/allDoCountLayout"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:layout_marginTop="4dp"
                            app:layout_constraintStart_toEndOf="@+id/line1View"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/line2View">

                            <TextView
                                android:id="@+id/allCountView"
                                android:text="0"
                                android:textColor="@color/tblack"
                                android:textSize="16dp"
                                style="@style/lightText"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>

                            <TextView
                                android:text="答题数"
                                android:textColor="@color/tblack3"
                                android:textSize="10dp"
                                style="@style/lightText"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>

                        <View
                            android:id="@+id/line2View"
                            android:background="@color/line_color"
                            android:layout_width="1dp"
                            android:layout_height="15dp"
                            app:layout_constraintStart_toEndOf="@id/allDoCountLayout"
                            app:layout_constraintTop_toTopOf="@id/allDoCountLayout"
                            app:layout_constraintBottom_toBottomOf="@id/allDoCountLayout"
                            app:layout_constraintEnd_toStartOf="@id/currentRateLayout"/>

                        <LinearLayout
                            android:id="@+id/currentRateLayout"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:layout_marginTop="4dp"
                            app:layout_constraintStart_toEndOf="@+id/line2View"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/line3View">

                            <com.dep.biguo.widget.DiversificationTextView
                                android:id="@+id/correctRateView"
                                android:text="0%"
                                android:textColor="@color/tblack"
                                android:textSize="16dp"
                                style="@style/lightText"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                app:startChar="%"
                                app:isStartFlag="true"
                                app:size="12dp"/>

                            <TextView
                                android:text="正确率"
                                android:textColor="@color/tblack3"
                                android:textSize="10dp"
                                style="@style/lightText"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>

                        <View
                            android:id="@+id/line3View"
                            android:background="@color/line_color"
                            android:layout_width="1dp"
                            android:layout_height="15dp"
                            app:layout_constraintStart_toEndOf="@id/currentRateLayout"
                            app:layout_constraintTop_toTopOf="@id/currentRateLayout"
                            app:layout_constraintBottom_toBottomOf="@id/currentRateLayout"
                            app:layout_constraintEnd_toStartOf="@id/studyTimeLayout"/>

                        <LinearLayout
                            android:id="@+id/studyTimeLayout"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:layout_marginTop="4dp"
                            app:layout_constraintStart_toEndOf="@id/line3View"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintEnd_toEndOf="parent">

                            <TextView
                                android:id="@+id/studyDuc"
                                android:text="0时0分"
                                android:textColor="@color/tblack"
                                android:textSize="16dp"
                                style="@style/lightText"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>

                            <TextView
                                android:text="已学习"
                                android:textColor="@color/tblack3"
                                android:textSize="10dp"
                                style="@style/lightText"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <com.biguo.utils.widget.StyleLinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="20dp"
                        android:orientation="vertical"
                        android:paddingTop="20dp"
                        android:paddingBottom="10dp"
                        app:bgGradientStartColor="@color/white"
                        app:bgcGradientAngle="90"
                        app:left_top_round="15dp"
                        app:right_top_round="15dp">

                        <com.dep.biguo.widget.HorizontalRecyclerView
                            android:id="@+id/recommendRecyclerView"
                            android:scrollbars="none"
                            android:orientation="horizontal"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            tools:listitem="@layout/topic_list_recommend_item"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>


                        <View
                            android:layout_width="match_parent"
                            android:layout_height="10dp"/>

                        <com.biguo.utils.widget.StyleLinearLayout
                            android:id="@+id/buyRecommendLayout"
                            android:onClick="@{onClickListener}"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingStart="15dp"
                            android:paddingEnd="15dp"
                            android:paddingTop="5dp"
                            android:paddingBottom="5dp"
                            android:gravity="center"
                            app:bgGradientStartColor="#1AFBBD41"
                            app:left_top_round="20dp"
                            app:right_top_round="20dp">
                            <ImageView
                                android:id="@+id/recommendIconView"
                                android:src="@drawable/chapter_open_membership"
                                android:layout_width="30dp"
                                android:layout_height="30dp"/>
                            <TextView
                                android:id="@+id/recommendContentView"
                                android:text="开通笔果折扣卡，畅享3大权益"
                                android:textColor="@color/china_mobil_text_color"
                                android:textSize="12dp"
                                android:layout_marginStart="7dp"
                                android:layout_weight="1"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"/>

                            <com.biguo.utils.widget.StyleTextView
                                android:text="立即开通"
                                android:textSize="12dp"
                                android:textColor="@color/twhite"
                                android:layout_width="70dp"
                                android:layout_height="wrap_content"
                                android:paddingVertical="2dp"
                                android:gravity="center"
                                app:all_round="20dp"
                                app:bgGradientStartColor="#FA6C64"
                                app:bgGradientEndColor="#D53E43"/>
                        </com.biguo.utils.widget.StyleLinearLayout>

                        <FrameLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <include layout="@layout/empty_topic_status"
                                android:id="@+id/emptyTopicStatusLayout"
                                android:visibility="gone"/>

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rvChapter"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:nestedScrollingEnabled="false"
                                tools:listitem="@layout/chapter_rv_head"
                                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>
                        </FrameLayout>
                    </com.biguo.utils.widget.StyleLinearLayout>
                </LinearLayout>
            </androidx.core.widget.NestedScrollView>
        </com.dep.biguo.widget.SmartRefreshLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>