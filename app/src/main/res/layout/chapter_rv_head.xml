<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/conlHead"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="@dimen/app_space"
    android:layout_marginRight="@dimen/app_space"
    android:paddingTop="@dimen/app_space"
    android:paddingBottom="@dimen/app_space">

    <TextView
        android:id="@+id/tvChapter"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/dp_5"
        android:drawableLeft="@drawable/chapter_icon_add"
        android:drawablePadding="@dimen/dp_15"
        android:textColor="@color/tblack"
        android:textSize="@dimen/sp_14"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tvBuy"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="章节名" />

    <TextView
        android:id="@+id/tvBuy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableRight="@drawable/chapter_icon_buy"
        android:drawablePadding="@dimen/dp_5"
        android:gravity="center"
        android:textColor="@color/tblack2"
        android:textSize="@dimen/sp_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>