<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@drawable/bg_round_5_white"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center">
    <TextView
        android:id="@+id/titleView"
        android:text="安全验证"
        android:textSize="16dp"
        android:textColor="@color/tblack3"
        android:layout_marginTop="12dp"
        android:gravity="center"
        android:layout_width="@dimen/dp_280"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>
    
    <ImageView
        android:id="@+id/imageVerifyView"
        android:layout_marginTop="8dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="96dp"
        android:minHeight="42dp"
        android:background="@color/tblack3"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titleView"
        app:layout_constraintEnd_toEndOf="parent"/>

    <EditText
        android:id="@+id/inputVerifyView"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:minWidth="116dp"
        android:layout_marginTop="8dp"
        android:background="@null"
        android:hint="请填写图形验证码"
        android:maxLines="1"
        android:maxLength="8"
        android:textSize="@dimen/sp_14"
        android:textColorHint="@color/tblack3"
        android:textColor="@color/tblack"
        app:layout_constraintStart_toStartOf="@id/imageVerifyView"
        app:layout_constraintTop_toBottomOf="@id/imageVerifyView"
        app:layout_constraintEnd_toEndOf="@id/imageVerifyView"/>

    <TextView
        android:id="@+id/positiveView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_8"
        android:background="@drawable/bg_rect_cwhite"
        android:paddingLeft="@dimen/dp_15"
        android:paddingTop="@dimen/dp_7"
        android:paddingRight="@dimen/dp_15"
        android:paddingBottom="@dimen/dp_7"
        android:textColor="@color/orange"
        android:text="@string/confirm"
        android:textSize="@dimen/sp_12"
        app:layout_constraintTop_toBottomOf="@id/inputVerifyView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/negativeView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/app_space"
        android:background="@drawable/bg_rect_cwhite"
        android:paddingLeft="@dimen/dp_15"
        android:paddingTop="@dimen/dp_7"
        android:paddingRight="@dimen/dp_15"
        android:paddingBottom="@dimen/dp_7"
        android:textColor="@color/orange"
        android:text="@string/cancel"
        android:textSize="@dimen/sp_12"
        app:layout_constraintTop_toTopOf="@id/positiveView"
        app:layout_constraintEnd_toStartOf="@id/positiveView"
        app:layout_constraintBottom_toBottomOf="@id/positiveView"/>

</androidx.constraintlayout.widget.ConstraintLayout>