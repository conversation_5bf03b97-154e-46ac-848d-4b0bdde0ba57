<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginLeft="@dimen/dp_10"
    android:layout_marginRight="@dimen/dp_10"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/dp_10"
    android:background="@drawable/bg_round_10_white"
    android:clickable="true"
    android:orientation="vertical"
    android:padding="@dimen/dp_10">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/vipcourse_tv_first"
            android:layout_width="@dimen/dp_15"
            android:layout_height="@dimen/dp_15"
            android:background="@drawable/round_rect_5_theme"
            android:gravity="center"
            android:text="荐"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_10" />

        <TextView
            android:id="@+id/vipcourse_tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_10"
            android:textColor="@color/tblack"
            android:textSize="@dimen/sp_14"
            tools:text="标题标题标题标题标题题标题标标题标题标题标题" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/icon_indent_time" />

        <TextView
            android:id="@+id/vipcourse_tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_3"
            android:textColor="@color/black3"
            android:textSize="@dimen/sp_12"
            tools:text="时间" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:orientation="horizontal">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_teachers"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right|bottom"
            android:gravity="right"
            android:orientation="vertical">

            <TextView
                android:id="@+id/vipcourse_tv_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="￥0"
                android:textColor="@color/theme"
                android:textSize="@dimen/sp_16" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_5"
                android:text="已有482人购买"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_12"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>