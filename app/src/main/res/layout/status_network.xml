<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:padding="10dp"
    android:layout_gravity="center"
    android:gravity="center"
    android:orientation="vertical">

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/status_network" />

    <com.dep.biguo.widget.DiversificationTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_50"
        android:text="@string/status_network"
        android:textColor="@color/tblack"
        android:gravity="center"
        android:lineSpacingExtra="10dp"
        android:textSize="@dimen/sp_16"
        app:startChar="\n"
        app:changeColor="@color/tblack3"
        app:size="12dp"/>

    <TextView
        android:id="@+id/refreshView"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:background="@drawable/border_round_200_theme"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        android:layout_marginTop="@dimen/dp_20"
        android:gravity="center"
        android:text="@string/refresh"
        android:textColor="@color/theme"
        android:textSize="@dimen/sp_12" />

</LinearLayout>