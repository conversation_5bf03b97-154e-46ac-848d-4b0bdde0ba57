<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tbVideo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:tabTextAppearance="@style/TabTextSize"
        app:tabIndicatorFullWidth="false"
        app:tabSelectedTextColor="@color/theme"
        app:tabTextColor="@color/tblack" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/vpVideo"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</LinearLayout>