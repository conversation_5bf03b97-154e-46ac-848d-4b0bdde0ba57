<?xml version="1.0" encoding="utf-8"?>
<layout>
    <com.dep.biguo.widget.SmartRefreshLayout
        android:id="@+id/swipeLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:android="http://schemas.android.com/apk/res/android">
        <androidx.core.widget.NestedScrollView
            android:background="@color/white"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingStart="@dimen/screenSpace"
                android:paddingEnd="@dimen/screenSpace"
                android:paddingBottom="20dp"
                android:background="@color/white">

                <com.biguo.utils.widget.StyleConstraintLayout
                    android:id="@+id/studyInfoLayout"
                    android:layout_width="match_parent"
                    android:layout_height="84dp"
                    android:layout_marginTop="12dp"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"
                    app:bgGradientStartColor="@color/white"
                    app:all_round="10dp"
                    app:shadowWidth="6dp"
                    app:shadowColor="#21D53E43"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/doQuestionCountView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:lines="2"
                        android:textSize="21dp"
                        android:textColor="@color/theme"
                        android:text="1000\n今日做题"
                        android:gravity="center"
                        android:layout_marginEnd="6dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/finishRateView"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"/>

                    <TextView
                        android:id="@+id/finishRateView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:lines="2"
                        android:text="100%\n完成率"
                        android:textSize="21dp"
                        android:textColor="@color/theme"
                        android:gravity="center"
                        android:layout_marginEnd="6dp"
                        app:layout_constraintStart_toEndOf="@id/doQuestionCountView"
                        app:layout_constraintTop_toTopOf="@id/doQuestionCountView"
                        app:layout_constraintEnd_toStartOf="@id/correctRateView"
                        app:layout_constraintBottom_toBottomOf="@id/doQuestionCountView"/>

                    <TextView
                        android:id="@+id/correctRateView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:lines="2"
                        android:text="100%\n完成率"
                        android:textSize="21dp"
                        android:textColor="@color/theme"
                        android:gravity="center"
                        android:layout_marginEnd="6dp"
                        app:layout_constraintStart_toEndOf="@id/finishRateView"
                        app:layout_constraintTop_toTopOf="@id/doQuestionCountView"
                        app:layout_constraintEnd_toStartOf="@id/studyTimeView"
                        app:layout_constraintBottom_toBottomOf="@id/doQuestionCountView"/>

                    <!--通过spannerString改变字符大小后，不能很好的实现对齐，因此设置一个最小高度-->
                    <TextView
                        android:id="@+id/studyTimeView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:lines="2"
                        android:text="9999时9999分\n已学时间"
                        android:textSize="21dp"
                        android:textColor="@color/theme"
                        android:gravity="center"
                        app:layout_constraintStart_toEndOf="@id/correctRateView"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/doQuestionCountView"
                        app:layout_constraintBottom_toBottomOf="@id/doQuestionCountView"/>

                </com.biguo.utils.widget.StyleConstraintLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/iconRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    tools:itemCount="4"
                    tools:listitem="@layout/study_icon_item"
                    app:spanCount="4"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/studyInfoLayout">

                </androidx.recyclerview.widget.RecyclerView>

                <com.youth.banner.Banner
                    android:id="@+id/bannerView"
                    android:layout_width="match_parent"
                    android:layout_height="34dp"
                    android:src="@drawable/study_super_vip"
                    android:layout_marginTop="15dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/iconRecyclerView"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <com.biguo.utils.widget.StyleLinearLayout
                    android:id="@+id/shadowLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    app:bgGradientStartColor="@color/white"
                    app:all_round="10dp"
                    app:shadowWidth="6dp"
                    app:shadowColor="#21D53E43"
                    app:layout_constraintTop_toBottomOf="@id/bannerView">
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/questionBankRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="20dp"
                        tools:itemCount="4"
                        tools:listitem="@layout/study_question_bank_item"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>
                </com.biguo.utils.widget.StyleLinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>
    </com.dep.biguo.widget.SmartRefreshLayout>
</layout>