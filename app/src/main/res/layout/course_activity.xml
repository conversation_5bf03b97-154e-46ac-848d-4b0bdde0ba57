<?xml version="1.0" encoding="utf-8"?>
<layout>
    <data>
        <variable
            name="onClickListener"
            type="com.dep.biguo.mvp.ui.activity.CourseActivity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <ImageView
            android:id="@+id/testPlanView"
            android:layout_width="90dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/screenSpace"
            android:onClick="@{onClickListener}"
            android:src="@drawable/test_plan_img"
            android:visibility="invisible"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/enrollTargetView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="48dp"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:text="主考院校："
            android:textColor="@color/tblack"
            android:textSize="14dp"
            app:layout_constraintBottom_toTopOf="@id/layerView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/testPlanView"
            app:layout_constraintTop_toTopOf="@id/testPlanView"
            app:layout_constraintVertical_chainStyle="spread_inside" />

        <TextView
            android:id="@+id/layerView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="层次："
            android:textColor="@color/tblack"
            android:textSize="14dp"
            app:layout_constraintBottom_toTopOf="@id/beforeProfessionView"
            app:layout_constraintEnd_toEndOf="@id/enrollTargetView"
            app:layout_constraintStart_toEndOf="@id/testPlanView"
            app:layout_constraintTop_toBottomOf="@id/enrollTargetView" />

        <TextView
            android:id="@+id/beforeProfessionView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="原专业："
            android:textColor="@color/tblack3"
            android:textSize="12dp"
            app:layout_constraintBottom_toTopOf="@id/testTimeView"
            app:layout_constraintEnd_toEndOf="@id/enrollTargetView"
            app:layout_constraintStart_toEndOf="@id/testPlanView"
            app:layout_constraintTop_toBottomOf="@id/layerView" />

        <TextView
            android:id="@+id/testTimeView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="@dimen/screenSpace"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="预计报考时间："
            android:textColor="@color/tblack"
            android:textSize="14dp"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/testPlanView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/testPlanView"
            app:layout_constraintTop_toBottomOf="@id/beforeProfessionView" />


        <ImageView
            android:id="@+id/weChatView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:onClick="@{onClickListener}"
            android:src="@drawable/province_wx_group"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/testPlanView" />

        <LinearLayout
            android:id="@+id/middleLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:orientation="horizontal"
            android:paddingStart="5dp"
            android:paddingEnd="5dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/testPlanView">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/assistiveClassLayout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:layout_marginEnd="5dp"
                android:layout_weight="1"
                android:onClick="@{onClickListener}">

                <ImageView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:scaleType="fitXY"
                    android:src="@drawable/assistive_class_bg"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/assistiveClassTitleView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:src="@drawable/assistive_class_title"
                    android:layout_marginTop="10dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="4dp"
                    app:layout_constraintStart_toEndOf="@id/assistiveClassTextView"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent">
                    <!--图片需要右对齐，但是scaleType设置fitEnd同时也底部对齐了，因此包裹一层布局-->
                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/assistive_class_right" />

                </LinearLayout>

                <TextView
                    android:id="@+id/assistiveClassTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:textColor="@color/tblack3"
                    android:textSize="9dp"
                    android:layout_marginEnd="50dp"
                    android:gravity="center_vertical"
                    android:lines="2"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/assistiveClassTitleView" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/studyPlanLayout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:layout_marginEnd="5dp"
                android:layout_weight="1"
                android:onClick="@{onClickListener}">

                <ImageView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:scaleType="fitXY"
                    android:src="@drawable/study_plan_bg"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/studyPlanTitleView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:src="@drawable/study_plan_title"
                    android:layout_marginTop="10dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="4dp"
                    app:layout_constraintStart_toEndOf="@id/studyPlanTextView"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent">
                    <!--图片需要右对齐，但是scaleType设置fitEnd同时也底部对齐了，因此包裹一层布局-->
                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/study_plan_right" />

                </LinearLayout>

                <TextView
                    android:id="@+id/studyPlanTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:textColor="@color/tblack3"
                    android:textSize="9dp"
                    android:layout_marginEnd="50dp"
                    android:gravity="center_vertical"
                    android:lines="2"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/studyPlanTitleView" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="20dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintTop_toBottomOf="@id/middleLayout"
            app:layout_constraintBottom_toTopOf="@id/newUserLayout"
            tools:listitem="@layout/course_enroll_item" />

        <FrameLayout
            android:id="@+id/newUserLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:layout_marginBottom="10dp"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:onClick="@{onClickListener}"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">
            <ImageView
                android:src="@drawable/course_new_user_background"
                android:scaleType="fitXY"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <LinearLayout
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <ImageView
                    android:src="@drawable/course_new_user_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15dp"/>

                <com.dep.biguo.widget.DiversificationTextView
                    android:id="@+id/bottomPriceView"
                    android:textSize="12dp"
                    android:textColor="#FBF656"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_marginEnd="5dp"/>

                <LinearLayout
                    android:id="@+id/countdownLayout"
                    android:gravity="center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <com.dep.biguo.widget.TimeView
                        android:id="@+id/hoursView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingStart="1dp"
                        android:paddingEnd="1dp"
                        android:background="@drawable/bg_round_2_white"
                        app:fontSize="11dp"
                        app:color="#FA6C64"
                        app:format="00"/>

                    <TextView
                        android:text=":"
                        android:textSize="11dp"
                        android:textColor="@color/twhite"
                        android:paddingStart="2dp"
                        android:paddingEnd="2dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>

                    <com.dep.biguo.widget.TimeView
                        android:id="@+id/minuteView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingStart="1dp"
                        android:paddingEnd="1dp"
                        android:background="@drawable/bg_round_2_white"
                        app:fontSize="11dp"
                        app:color="#FA6C64"
                        app:format="00"/>

                    <TextView
                        android:text=":"
                        android:textSize="11dp"
                        android:textColor="@color/twhite"
                        android:paddingStart="2dp"
                        android:paddingEnd="2dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>

                    <com.dep.biguo.widget.TimeView
                        android:id="@+id/secondView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingStart="1dp"
                        android:paddingEnd="1dp"
                        android:background="@drawable/bg_round_2_white"
                        app:fontSize="11dp"
                        app:color="#FA6C64"
                        app:format="00"/>
                </LinearLayout>

                <LinearLayout
                    android:gravity="end"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content">
                    <ImageView
                        android:src="@drawable/course_new_user_open"
                        android:layout_marginStart="5dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                </LinearLayout>

                <TextView
                    android:id="@+id/closeNewUserLayout"
                    android:text="×"
                    android:textColor="@color/twhite"
                    android:textSize="20dp"
                    android:gravity="center_horizontal"
                    android:layout_width="22dp"
                    android:layout_height="match_parent"
                    android:onClick="@{onClickListener}"
                    style="@style/lightText"/>
            </LinearLayout>

        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>