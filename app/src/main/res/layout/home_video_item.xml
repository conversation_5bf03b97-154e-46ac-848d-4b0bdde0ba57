<?xml version="1.0" encoding="utf-8"?>
<com.biguo.utils.widget.StyleConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="wrap_content"
    android:gravity="center_vertical"
    android:layout_height="185dp"
    app:bgGradientStartColor="@color/white"
    app:all_round="5dp"
    app:shadowWidth="14dp"
    app:shadowColor="@color/gray_gradient_1_bg">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/headLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/bgView"
            android:layout_width="160dp"
            android:layout_height="wrap_content"
            android:scaleType="fitXY"
            android:src="@drawable/bg_top_group_video_jingjiang"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/bgNameView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_15"
            android:text="中国近现代史纲要"
            android:maxLines="2"
            android:ellipsize="end"
            android:textColor="@color/black"
            android:textSize="16dp"
            style="@style/boldText"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/bgCodeView"/>

        <com.biguo.utils.widget.StyleTextView
            android:id="@+id/bgCodeView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingLeft="@dimen/dp_5"
            android:paddingRight="@dimen/dp_5"
            android:paddingTop="@dimen/dp_2"
            android:paddingBottom="@dimen/dp_2"
            android:text="@string/group_subject_code"
            android:textColor="@color/twhite"
            android:layout_marginTop="5dp"
            android:textSize="9dp"
            app:bgGradientStartColor="#FC3D3E"
            app:bgGradientEndColor="#FE5A5A"
            app:all_round="20dp"
            app:layout_constraintStart_toStartOf="@+id/bgNameView"
            app:layout_constraintTop_toBottomOf="@id/bgNameView"
            app:layout_constraintBottom_toBottomOf="@id/bgView"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.dep.biguo.widget.DiversificationTextView
        android:id="@+id/groupPriceView"
        android:text="¥00.00"
        android:textSize="16dp"
        android:textColor="@color/theme"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        app:startChar="¥"
        app:size="21dp"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="@id/headLayout"
        app:layout_constraintBottom_toTopOf="@id/buyCountView"/>

    <com.dep.biguo.widget.DiversificationTextView
        android:id="@+id/priceView"
        android:text="¥00.00"
        android:textSize="10dp"
        android:textColor="@color/tblack3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:layout_marginBottom="2dp"
        app:startChar="¥"
        app:size="12dp"
        app:line_position="middle"
        app:layout_constraintStart_toEndOf="@id/groupPriceView"
        app:layout_constraintBottom_toBottomOf="@+id/groupPriceView"/>

    <com.dep.biguo.widget.DiversificationTextView
        android:id="@+id/statusView"
        android:text="已开通课程"
        android:textSize="12dp"
        android:textColor="@color/theme"
        android:gravity="center_vertical"
        android:visibility="gone"
        android:drawablePadding="5dp"
        android:drawableEnd="@drawable/video_open_status"
        android:layout_width="wrap_content"
        android:layout_height="28dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="10dp"
        app:layout_constraintStart_toStartOf="@id/headLayout"
        app:layout_constraintBottom_toTopOf="@id/buyCountView"/>

    <com.dep.biguo.widget.DiversificationTextView
        android:id="@+id/buyCountView"
        android:text="0人已买"
        android:textSize="10dp"
        android:textColor="@color/tblack3"
        android:gravity="center_vertical"
        android:layout_width="wrap_content"
        android:layout_height="28dp"
        android:layout_marginStart="10dp"
        app:layout_constraintStart_toStartOf="@id/headLayout"
        app:layout_constraintBottom_toBottomOf="parent"/>
</com.biguo.utils.widget.StyleConstraintLayout>