<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/app_space"
    android:background="@drawable/bg_round_10_white"
    android:padding="@dimen/app_space">

    <TextView
        android:id="@+id/tvShare"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/share"
        android:textColor="@color/tblack"
        android:textSize="@dimen/sp_12"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvShareHint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/share_hint"
        android:textColor="@color/tblack3"
        android:visibility="gone"
        android:textSize="@dimen/sp_12"
        android:textStyle="bold"
        android:layout_marginTop="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvShare" />

    <TextView
        android:id="@+id/tvWechat"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/app_space"
        android:drawableTop="@drawable/share_icon_wechat"
        android:drawablePadding="@dimen/dp_5"
        android:gravity="center_horizontal"
        android:text="@string/share_wechat"
        android:textColor="@color/tblack3"
        android:textSize="@dimen/sp_10"
        app:layout_constraintEnd_toStartOf="@+id/tvCircle"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvShareHint" />

    <TextView
        android:id="@+id/tvCircle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:drawableTop="@drawable/share_icon_circle"
        android:drawablePadding="@dimen/dp_5"
        android:gravity="center_horizontal"
        android:text="@string/share_circle"
        android:textColor="@color/tblack3"
        android:textSize="@dimen/sp_10"
        app:layout_constraintEnd_toStartOf="@+id/tvQq"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/tvWechat"
        app:layout_constraintTop_toBottomOf="@+id/tvShare"
        app:layout_constraintTop_toTopOf="@+id/tvWechat" />

    <TextView
        android:id="@+id/tvQq"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:drawableTop="@drawable/share_icon_qq"
        android:drawablePadding="@dimen/dp_5"
        android:gravity="center_horizontal"
        android:text="@string/share_qq"
        android:textColor="@color/tblack3"
        android:textSize="@dimen/sp_10"
        app:layout_constraintEnd_toStartOf="@+id/tvQzone"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/tvCircle"
        app:layout_constraintTop_toBottomOf="@+id/tvShare"
        app:layout_constraintTop_toTopOf="@+id/tvWechat" />

    <TextView
        android:id="@+id/tvQzone"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:drawableTop="@drawable/share_icon_qzone"
        android:drawablePadding="@dimen/dp_5"
        android:gravity="center_horizontal"
        android:text="@string/share_qzone"
        android:textColor="@color/tblack3"
        android:textSize="@dimen/sp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/tvQq"
        app:layout_constraintTop_toBottomOf="@+id/tvShare"
        app:layout_constraintTop_toTopOf="@+id/tvWechat" />

    <View
        android:id="@+id/line"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginTop="@dimen/app_space"
        android:background="@color/line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvWechat" />

    <TextView
        android:id="@+id/tvCancel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingTop="@dimen/app_space"
        android:text="@string/cancel"
        android:textColor="@color/tblack"
        android:textSize="@dimen/sp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/line" />

</androidx.constraintlayout.widget.ConstraintLayout>