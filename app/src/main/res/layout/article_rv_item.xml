<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/app_space">

    <TextView
        android:id="@+id/tvTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/tblack4"
        android:textSize="@dimen/sp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.dep.biguo.widget.RoundedImageView
        android:id="@+id/ivImg"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_150"
        android:layout_marginTop="@dimen/dp_10"
        android:scaleType="centerCrop"
        app:radius_top_left="10dp"
        app:radius_top_right="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTime" />

    <TextView
        android:id="@+id/tvNumber"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/gradient_black"
        android:paddingLeft="@dimen/app_space"
        android:paddingTop="@dimen/app_space"
        android:paddingRight="@dimen/app_space"
        android:paddingBottom="@dimen/dp_5"
        android:text="浏览量"
        android:textColor="@color/twhite"
        android:textSize="@dimen/sp_14"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/ivImg"
        app:layout_constraintEnd_toEndOf="@id/ivImg"
        app:layout_constraintStart_toStartOf="@id/ivImg" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_round_bot_10_white"
        android:padding="@dimen/app_space"
        android:textColor="@color/tblack"
        android:textSize="@dimen/sp_14"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivImg" />

</androidx.constraintlayout.widget.ConstraintLayout>