<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_round_10_white">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/app_space"
            android:text="安全验证"
            android:textColor="@color/tblack"
            android:textSize="@dimen/sp_12" />

        <ImageView
            android:id="@+id/ivCode"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tvTitle"
            android:layout_centerHorizontal="true"
            android:layout_marginLeft="@dimen/app_space"
            android:layout_marginTop="@dimen/app_space"
            android:layout_marginRight="@dimen/app_space" />

        <EditText
            android:id="@+id/etCode"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/ivCode"
            android:layout_marginLeft="@dimen/app_space"
            android:layout_marginTop="@dimen/app_space"
            android:layout_marginRight="@dimen/app_space"
            android:background="@null"
            android:hint="请填写图形验证码"
            android:paddingTop="@dimen/dp_5"
            android:paddingBottom="@dimen/dp_5"
            android:textColor="@color/tblack"
            android:textColorHint="@color/black3"
            android:textSize="@dimen/sp_12" />

        <TextView
            android:id="@+id/tvNegative"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/etCode"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginRight="@dimen/dp_5"
            android:layout_marginBottom="@dimen/dp_8"
            android:layout_toLeftOf="@+id/tvPositive"
            android:background="@drawable/bg_rect_cwhite"
            android:clickable="true"
            android:paddingLeft="@dimen/dp_15"
            android:paddingTop="@dimen/dp_7"
            android:paddingRight="@dimen/dp_15"
            android:paddingBottom="@dimen/dp_7"
            android:text="@string/cancel"
            android:textColor="@color/orange"
            android:textSize="@dimen/sp_12" />

        <TextView
            android:id="@+id/tvPositive"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/etCode"
            android:layout_alignParentRight="true"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginRight="@dimen/app_space"
            android:layout_marginBottom="@dimen/dp_8"
            android:background="@drawable/bg_rect_cwhite"
            android:clickable="true"
            android:paddingLeft="@dimen/dp_15"
            android:paddingTop="@dimen/dp_7"
            android:paddingRight="@dimen/dp_15"
            android:paddingBottom="@dimen/dp_7"
            android:text="@string/confirm"
            android:textColor="@color/orange"
            android:textSize="@dimen/sp_12" />

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_80" />

</LinearLayout>