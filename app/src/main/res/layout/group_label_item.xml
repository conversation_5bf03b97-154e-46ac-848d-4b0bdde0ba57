<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:paddingStart="0dp"
    android:paddingEnd="10dp"
    android:paddingBottom="4dp">
    <!--不单独使用imageView控件展示图标，则FlexboxLayoutManager会导致item测量出错-->
    <ImageView
        android:src="@drawable/icon_hook"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/labelView"
        android:textSize="10dp"
        android:text="2人成团"
        android:layout_marginStart="2dp"
        android:textColor="@color/tblack2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>
</LinearLayout>