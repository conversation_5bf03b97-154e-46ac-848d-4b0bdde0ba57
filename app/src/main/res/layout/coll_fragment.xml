<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="onClickListener"
            type="com.dep.biguo.mvp.ui.fragment.CollFragment" />
    </data>
    <com.dep.biguo.widget.SmartRefreshLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/swipeLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <androidx.core.widget.NestedScrollView
            android:fillViewport="true"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:gravity="center_vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="15dp"
                    android:layout_marginBottom="10dp">
                    <TextView
                        android:text="收藏统计"
                        android:textSize="18dp"
                        android:textColor="@color/tblack"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        style="@style/boldText"/>

                    <TextView
                        android:id="@+id/clearView"
                        android:text="清空"
                        android:textSize="14dp"
                        android:textColor="@color/tblack"
                        android:onClick="@{onClickListener}"
                        android:drawableStart="@drawable/error_coll_clear"
                        android:drawablePadding="4dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                </LinearLayout>

                <com.biguo.utils.widget.StyleLinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:padding="10dp"
                    app:all_round="6dp"
                    app:bgGradientStartColor="@color/white">
                    <com.dep.biguo.widget.DiversificationTextView
                        android:id="@+id/allCollCountView"
                        android:text="收藏总数\n0"
                        android:textSize="14dp"
                        android:textColor="@color/tblack2"
                        android:gravity="center"
                        android:lineSpacingExtra="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        app:startChar="\n"
                        app:size="22dp"
                        app:changeColor="@color/tblack"/>

                    <View
                        android:background="@color/line"
                        android:layout_marginTop="5dp"
                        android:layout_marginBottom="5dp"
                        android:layout_width="1dp"
                        android:layout_height="match_parent"/>

                    <com.dep.biguo.widget.DiversificationTextView
                        android:id="@+id/dayCollCountView"
                        android:text="今日收藏\n0"
                        android:textSize="14dp"
                        android:textColor="@color/tblack2"
                        android:gravity="center"
                        android:lineSpacingExtra="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        app:startChar="\n"
                        app:size="22dp"
                        app:changeColor="@color/tblack"/>

                </com.biguo.utils.widget.StyleLinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:orientation="vertical"
                    android:paddingTop="20dp"
                    android:paddingBottom="10dp"
                    android:background="@drawable/error_coll_bg">

                    <TextView
                        android:text="收藏分布"
                        android:textSize="18dp"
                        android:textColor="@color/tblack"
                        android:layout_marginStart="15dp"
                        android:layout_marginEnd="15dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        tools:itemCount="6"
                        tools:listitem="@layout/mycoll_rv_item"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>
                </LinearLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </com.dep.biguo.widget.SmartRefreshLayout>
</layout>