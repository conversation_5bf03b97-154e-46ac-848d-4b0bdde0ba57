<?xml version="1.0" encoding="utf-8"?>
<layout>
    <data>
        <variable
            name="onClickListener"
            type="com.dep.biguo.widget.QuestionView" />
        <variable
            name="onLongClickListener"
            type="com.dep.biguo.widget.QuestionView" />
    </data>
    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:orientation="vertical"
        android:background="@color/white"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!--layout_marginTop设置为负数，是为了适配text中添加换行符，
            添加换行符才能让TextView自适应<sub></sub>标签，否则高度不够，将会遮挡部分文字-->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/questionView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineSpacingMultiplier="1.3"
                android:includeFontPadding="false"
                android:paddingStart="@dimen/screenSpace"
                android:layout_marginTop="10dp"
                android:paddingEnd="@dimen/screenSpace"
                android:layout_marginBottom="15dp"
                android:onLongClickListener="@{onLongClickListener.onLongClick}"
                android:text="题目"
                android:textColor="@color/tblack"
                android:textSize="16dp"
                style="@style/lightText"/>

        </FrameLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/optionsRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false"
            android:overScrollMode="never"
            android:scrollbars="none"
            tools:itemCount="2"
            tools:listitem="@layout/practice_single_option_v2_item"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="vertical"
            android:layout_marginStart="@dimen/screenSpace"
            android:layout_marginEnd="@dimen/screenSpace">

            <TextView
                android:id="@+id/myAnswerView"
                android:text="我的答案：\n未作答"
                android:textSize="16dp"
                android:textColor="@color/tblack"
                android:layout_marginBottom="25dp"
                android:onLongClickListener="@{onLongClickListener.onLongClick}"
                android:lineSpacingMultiplier="1.3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                style="@style/lightText"/>

            <com.dep.biguo.widget.LayoutUpScrollEditView
                android:id="@+id/inputAnswerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="请输入答案"
                android:visibility="gone"
                android:textColorHint="@color/black4"
                android:lineSpacingExtra="5dp"
                android:textSize="16dp"
                android:textColor="@color/tblack"
                style="@style/lightText"
                android:gravity="start"
                android:padding="15dp"
                android:minLines="3"
                android:maxLines="14"
                android:background="@drawable/bg_round_10_gray"/>

            <TextView
                android:id="@+id/remarksView"
                android:text="本题不支持系统判断正确错误，请自行判断"
                android:visibility="gone"
                android:textStyle="normal"
                android:textSize="12dp"
                android:textColor="@color/tblack3"
                android:layout_marginBottom="25dp"
                android:gravity="center"
                android:layout_marginTop="2dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </LinearLayout>

        <TextView
            android:id="@+id/commitAnswerView"
            android:gravity="center"
            android:layout_width="@dimen/dp_120"
            android:layout_height="@dimen/dp_32"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="25dp"
            android:onClickListener="@{onClickListener.onClick}"
            android:background="@drawable/bg_round_200_alpha_theme"
            android:clickable="false"
            android:text="确认答案"
            android:textColor="@color/twhite"
            android:textSize="16dp"
            style="@style/normalText"/>

        <com.biguo.utils.widget.StyleTextView
            android:id="@+id/correctAnswerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="25dp"
            android:minHeight="42dp"
            android:text="答案 A　您的答案 A"
            android:textSize="16dp"
            android:gravity="center_vertical"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:layout_marginStart="@dimen/screenSpace"
            android:layout_marginEnd="@dimen/screenSpace"
            android:lineSpacingExtra="@dimen/dp_5"
            android:textColor="@color/tblack"
            android:onLongClickListener="@{onLongClickListener.onLongClick}"
            style="@style/normalText"
            app:all_round="5dp"
            app:bgGradientStartColor="@color/theme_alpha_5"
            app:layout_constrainedWidth="true"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>


        <com.biguo.utils.widget.StyleConstraintLayout
            android:id="@+id/resultLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <View
                android:id="@+id/lineView1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_10"
                android:background="@color/line" />

            <com.biguo.utils.widget.StyleTextView
                android:id="@+id/reportErrorView"
                android:layout_width="wrap_content"
                android:layout_height="22dp"
                android:background="@drawable/report_question_error"
                android:onClickListener="@{onClickListener.onClick}"
                android:layout_marginEnd="10dp"
                app:layout_constraintTop_toTopOf="@+id/flResult"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="@+id/flResult" />

            <TextView
                android:id="@+id/flResult"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="15dp"
                android:drawableStart="@drawable/group_detail_left"
                android:drawableEnd="@drawable/group_detail_right"
                android:drawablePadding="8dp"
                android:textStyle="bold"
                android:gravity="center"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:text="题目解析"
                android:textSize="18dp"
                android:textColor="@color/tblack"
                style="@style/boldText"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/lineView1"/>


            <com.dep.biguo.widget.DiversificationTextView
                android:id="@+id/answerView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:lineSpacingMultiplier="1.3"
                android:textColor="@color/tblack"
                android:textSize="16dp"
                android:onLongClickListener="@{onLongClickListener.onLongClick}"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:layout_marginTop="20dp"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/flResult"
                tools:text="参考答案"
                style="@style/lightText"/>

            <TextView
                android:id="@+id/videoTextView"
                android:text="AI视频解析"
                android:textColor="@color/tblack"
                android:textSize="16dp"
                style="@style/lightText"
                android:layout_marginTop="@dimen/dp_20"
                android:layout_marginStart="10dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/answerView"/>

            <RelativeLayout
                android:id="@+id/videoLayout"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:layout_marginTop="10dp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/videoTextView"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintDimensionRatio="h,16:9">
                <com.dep.biguo.widget.VideoPlayerView
                    android:id="@+id/videoView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:onClick="@{onClickListener}"/>

                <RelativeLayout
                    android:id="@+id/openMembershipLayout"
                    android:layout_alignParentBottom="true"
                    android:layout_marginStart="14dp"
                    android:layout_marginBottom="14dp"
                    android:onClick="@{onClickListener}"
                    android:visibility="gone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <ImageView
                        android:id="@+id/openIconView"
                        android:src="@drawable/video_open"
                        android:layout_centerVertical="true"
                        android:translationZ="1px"
                        android:layout_width="27dp"
                        android:layout_height="27dp"/>

                    <com.dep.biguo.widget.DiversificationTextView
                        android:id="@+id/openMembershipTextView"
                        android:text="可免费观看3个视频，开通折扣卡观看全部"
                        android:textColor="@color/twhite"
                        android:textSize="14dp"
                        android:gravity="center_vertical"
                        android:layout_marginStart="13.5dp"
                        android:paddingStart="23.5dp"
                        android:paddingEnd="10dp"
                        android:background="@drawable/alpha_50_black_right_round"
                        android:layout_centerVertical="true"
                        android:layout_width="wrap_content"
                        android:layout_height="25dp"
                        app:startChar="开"
                        app:isStartFlag="true"
                        app:changeColor="#FFDE25"
                        app:endChar="卡"
                        app:isEndFlag="true"/>
                </RelativeLayout>

                <!--endPlayLayout添加点击事件是为了不让事件往下传递-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/endPlayLayout"
                    android:visibility="gone"
                    android:onClick="@{onClickListener}"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="#B3000000">
                    <TextView
                        android:id="@+id/videoMessageView"
                        android:text="开通折扣卡，可观看所有视频"
                        android:textSize="14dp"
                        android:textColor="@color/twhite"
                        android:gravity="center"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="20dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="20dp"
                        app:layout_constraintVertical_chainStyle="packed"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintBottom_toTopOf="@id/resetPlayView"/>
                    
                    <com.biguo.utils.widget.StyleTextView
                        android:id="@+id/resetPlayView"
                        android:text="重播"
                        android:textSize="14dp"
                        android:textColor="@color/twhite"
                        android:paddingStart="15dp"
                        android:paddingTop="6dp"
                        android:paddingEnd="15dp"
                        android:paddingBottom="6dp"
                        android:drawableStart="@drawable/video_reset"
                        android:drawablePadding="10dp"
                        android:layout_marginEnd="20dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:onClick="@{onClickListener}"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/videoMessageView"
                        app:layout_constraintEnd_toStartOf="@id/endPlayOpenMembershipView"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:all_round="60dp"
                        app:borderWidth="1dp"
                        app:borderColor="@color/twhite"/>

                    <com.biguo.utils.widget.StyleTextView
                        android:id="@+id/endPlayOpenMembershipView"
                        android:text="开通折扣卡"
                        android:textSize="14dp"
                        android:textColor="@color/twhite"
                        android:paddingStart="15dp"
                        android:paddingTop="6dp"
                        android:paddingEnd="15dp"
                        android:paddingBottom="6dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:onClick="@{onClickListener}"
                        app:layout_constraintStart_toEndOf="@id/resetPlayView"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/resetPlayView"
                        app:layout_constraintBottom_toBottomOf="@id/resetPlayView"
                        app:all_round="60dp"
                        app:bgGradientStartColor="#FA6C64"
                        app:bgGradientEndColor="#D53E43"/>
                </androidx.constraintlayout.widget.ConstraintLayout>

                <!--noPlayLayout添加点击事件是为了不让事件往下传递-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/noPlayLayout"
                    android:visibility="gone"
                    android:onClick="@{onClickListener}"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="#B3000000">
                    <TextView
                        android:id="@+id/videoNoPlayMessageView"
                        android:text="试看次数已用完，开通折扣卡可观看全部视频"
                        android:textSize="14dp"
                        android:textColor="@color/twhite"
                        android:gravity="center"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="20dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="20dp"
                        app:layout_constraintVertical_chainStyle="packed"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintBottom_toTopOf="@id/noPlayOpenMembershipView"/>

                    <com.biguo.utils.widget.StyleTextView
                        android:id="@+id/noPlayOpenMembershipView"
                        android:text="开通折扣卡"
                        android:textSize="14dp"
                        android:textColor="@color/twhite"
                        android:paddingStart="15dp"
                        android:paddingTop="6dp"
                        android:paddingEnd="15dp"
                        android:paddingBottom="6dp"
                        android:onClick="@{onClickListener}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/videoNoPlayMessageView"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:all_round="60dp"
                        app:bgGradientStartColor="#FA6C64"
                        app:bgGradientEndColor="#D53E43"/>
                </androidx.constraintlayout.widget.ConstraintLayout>
            </RelativeLayout>

            <TextView
                android:id="@+id/analyzeTitleView"
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:text="文字详解"
                android:textColor="@color/tblack"
                android:textSize="16dp"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:layout_marginTop="20dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/videoLayout"/>

            <com.dep.biguo.widget.DiversificationTextView
                android:id="@+id/analyzeView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:lineSpacingMultiplier="1.3"
                android:textColor="@color/tblack"
                android:textSize="16dp"
                android:onLongClickListener="@{onLongClickListener.onLongClick}"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:layout_marginTop="14dp"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/analyzeTitleView"
                tools:text="内容"
                style="@style/lightText"/>


            <com.biguo.utils.widget.StyleTextView
                android:id="@+id/aiAnalyzeView"
                android:text="点击获取AI解析"
                android:textColor="@color/twhite"
                android:textSize="13dp"
                android:gravity="center"
                android:layout_marginEnd="10dp"
                android:layout_width="110dp"
                android:layout_height="28dp"
                android:onClick="@{onClickListener.onClick}"
                style="@style/normalText"
                app:all_round="50dp"
                app:bgGradientStartColor="#1087FF"
                app:bgGradientEndColor="#28F5FF"
                app:layout_constraintTop_toTopOf="@+id/analyzeTitleView"
                app:layout_constraintBottom_toBottomOf="@id/analyzeTitleView"
                app:layout_constraintEnd_toEndOf="parent"/>
        </com.biguo.utils.widget.StyleConstraintLayout>

        <LinearLayout
            android:id="@+id/commentLayout"
            android:layout_marginTop="15dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <View
                android:id="@+id/lineView"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_10"
                android:background="@color/line" />

            <TextView
                android:id="@+id/commentTitleView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="15dp"
                android:drawableStart="@drawable/group_detail_left"
                android:drawableEnd="@drawable/group_detail_right"
                android:textStyle="bold"
                android:drawablePadding="8dp"
                android:gravity="center"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:text="考友点评"
                android:textSize="18dp"
                android:textColor="@color/tblack"
                style="@style/boldText"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/correctAnswerView"/>

            <TextView
                android:id="@+id/commentView"
                android:layout_marginStart="@dimen/screenSpace"
                android:layout_marginTop="@dimen/app_space"
                android:layout_marginEnd="@dimen/screenSpace"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:gravity="center_vertical"
                android:background="@drawable/bg_round_27_gray_n"
                android:backgroundTint="@color/group_good_comment_input_bg"
                android:onClickListener="@{onClickListener.onClick}"
                android:drawableStart="@drawable/practice_comment"
                android:drawablePadding="10dp"
                android:paddingStart="@dimen/screenSpace"
                android:paddingEnd="@dimen/screenSpace"
                android:text="发表一下你的看法吧～"
                android:textColor="@color/tblack3"
                android:textSize="@dimen/sp_14"
                style="@style/lightText" />

        </LinearLayout>
    </LinearLayout>
</layout>
