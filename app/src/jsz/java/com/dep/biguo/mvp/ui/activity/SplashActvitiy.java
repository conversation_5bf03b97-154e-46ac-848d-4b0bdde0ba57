package com.dep.biguo.mvp.ui.activity;

import android.os.Bundle;
import android.os.Handler;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.dep.biguo.R;
import com.jess.arms.utils.ArmsUtils;

public class SplashActvitiy extends AppCompatActivity {

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        setTheme(R.style.AppTheme_SplashStyle);
        super.onCreate(savedInstanceState);
//        if (!KVHelper.getBoolean(UserHelper.GUIDE)) {
//            ArmsUtils.startActivity(GuideActivity.class);
//        } else {
//            ArmsUtils.startActivity(JSZMainActivity.class);
//        }
        new Handler().postDelayed(() -> {
            ArmsUtils.startActivity(JSZMainActivity.class);
            finish();
        }, 1000);


    }
}
