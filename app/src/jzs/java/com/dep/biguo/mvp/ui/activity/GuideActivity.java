//package com.dep.biguo.mvp.ui.activity;
//
//import android.os.Bundle;
//import android.support.annotation.NonNull;
//import android.support.annotation.Nullable;
//import android.support.v4.content.ContextCompat;
//import android.support.v4.view.PagerAdapter;
//import android.support.v4.view.ViewPager;
//import android.view.LayoutInflater;
//import android.view.View;
//import android.view.ViewGroup;
//import android.widget.ImageView;
//import android.widget.TextView;
//
//import com.dep.biguo.R;
//import com.dep.biguo.utils.StatusBarHelper;
//import com.dep.biguo.utils.mmkv.KVHelper;
//import com.dep.biguo.utils.mmkv.UserHelper;
//import com.jess.arms.base.BaseActivity;
//import com.jess.arms.di.component.AppComponent;
//import com.jess.arms.utils.ArmsUtils;
//
//import java.util.ArrayList;
//import java.util.List;
//
//import butterknife.BindArray;
//import butterknife.BindView;
//
//public class GuideActivity extends BaseActivity {
//
////    @BindView(R.id.vpGuide)
////    ViewPager vpGuide;
////    @BindArray(R.array.guide_title)
////    String[] mTitles;
////    @BindArray(R.array.guide_subtitle)
////    String[] mSubTitles;
//
////    private int[] mIcons = {R.drawable.guide_icon_1, R.drawable.guide_icon_2, R.drawable.guide_icon_3, R.drawable.guide_icon_4};
////    private int[] mIndicators = {R.drawable.guide_indicator_1, R.drawable.guide_indicator_2, R.drawable.guide_indicator_3};
////
////    private List<View> mPagerViews;
//
//    @Override
//    public void setupActivityComponent(@NonNull AppComponent appComponent) {
//
//    }
//
//    @Override
//    public int initView(@Nullable Bundle savedInstanceState) {
//        return R.layout.guide_activity;
//    }
//
//    @Override
//    public void initData(@Nullable Bundle savedInstanceState) {
////        StatusBarHelper.setStatusbarColor(this, ContextCompat.getColor(this, R.color.white));
////        initPagerView();
////
////        vpGuide.setAdapter(new PagerAdapter() {
////
////            @Override
////            public Object instantiateItem(ViewGroup container, int position) {
////                container.addView(mPagerViews.get(position));
////
////                return mPagerViews.get(position);
////            }
////
////            @Override
////            public void destroyItem(ViewGroup container, int position, Object object) {
////                container.removeView(mPagerViews.get(position));
////            }
////
////            @Override
////            public int getCount() {
////                return mPagerViews.size();
////            }
////
////            @Override
////            public boolean isViewFromObject(@NonNull View view, @NonNull Object o) {
////                return view == o;
////            }
////        });
//    }
//
//    private void initPagerView() {
////        mPagerViews = new ArrayList<>();
////
////        for (int i = 0; i < mIcons.length; i++) {
////            View view = LayoutInflater.from(this).inflate(R.layout.guide_paper, null);
////            ((ImageView) view.findViewById(R.id.ivImage)).setImageResource(mIcons[i]);
////
////            if (i <= 2)
////                ((ImageView) view.findViewById(R.id.ivIndicator)).setImageResource(mIndicators[i]);
////
////            ((TextView) view.findViewById(R.id.tvTitle)).setText(mTitles[i]);
////            ((TextView) view.findViewById(R.id.tvSubtitle)).setText(mSubTitles[i]);
////
////            if (i == mIcons.length - 1) {
////                view.findViewById(R.id.tvFinish).setVisibility(View.VISIBLE);
////                view.findViewById(R.id.tvFinish).setOnClickListener(v -> {
////                    ArmsUtils.startActivity(JSZMainActivity.class);
////                    KVHelper.putValue(UserHelper.GUIDE, true);
////                    finish();
////                });
////            }
////
////            mPagerViews.add(view);
////        }
//    }
//
//}
